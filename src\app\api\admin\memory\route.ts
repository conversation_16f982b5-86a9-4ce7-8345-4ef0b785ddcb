import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { getUserFromToken } from '@/lib/auth';
import { cookies } from 'next/headers';
import { MemoryInsert } from '@/types/database';

// GET /api/admin/memory - Get memory entries (admin only)
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const cookieStore = await cookies();
    const token = cookieStore.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = await getUserFromToken(token);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const category = searchParams.get('category');

    let query = supabase
      .from('memory')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (category) {
      query = query.eq('category', category);
    }

    const { data: memories, error } = await query;

    if (error) {
      console.error('Memory fetch error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch memory entries' },
        { status: 500 }
      );
    }

    return NextResponse.json({ memories });
  } catch (error) {
    console.error('Memory API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/memory - Create memory entry (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const cookieStore = await cookies();
    const token = cookieStore.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = await getUserFromToken(token);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { question, answer, keywords, category, confidence_threshold } = body;

    // Validate input
    if (!question || !answer) {
      return NextResponse.json(
        { error: 'Question and answer are required' },
        { status: 400 }
      );
    }

    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return NextResponse.json(
        { error: 'At least one keyword is required' },
        { status: 400 }
      );
    }

    // Check if similar memory entry already exists
    const { data: existingMemory } = await supabase
      .from('memory')
      .select('id, question, answer')
      .eq('question', question.trim())
      .eq('is_active', true)
      .single();

    if (existingMemory) {
      // Update existing memory entry
      const { data: updatedMemory, error: updateError } = await supabase
        .from('memory')
        .update({
          answer: answer.trim(),
          keywords: keywords.map((k: string) => k.toLowerCase().trim()),
          category: category || 'admin_correction',
          confidence_threshold: confidence_threshold || 0.8,
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingMemory.id)
        .select()
        .single();

      if (updateError) {
        console.error('Memory update error:', updateError);
        return NextResponse.json(
          { error: 'Failed to update memory entry' },
          { status: 500 }
        );
      }

      return NextResponse.json({ 
        memory: updatedMemory, 
        message: 'Memory entry updated successfully' 
      });
    } else {
      // Create new memory entry
      const memoryData: MemoryInsert = {
        question: question.trim(),
        answer: answer.trim(),
        keywords: keywords.map((k: string) => k.toLowerCase().trim()),
        category: category || 'admin_correction',
        confidence_threshold: confidence_threshold || 0.8,
        is_active: true,
      };

      const { data: memory, error } = await supabase
        .from('memory')
        .insert(memoryData)
        .select()
        .single();

      if (error) {
        console.error('Memory creation error:', error);
        return NextResponse.json(
          { error: 'Failed to create memory entry' },
          { status: 500 }
        );
      }

      return NextResponse.json({ 
        memory, 
        message: 'Memory entry created successfully' 
      }, { status: 201 });
    }
  } catch (error) {
    console.error('Memory API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/memory - Update memory entry (admin only)
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const cookieStore = await cookies();
    const token = cookieStore.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = await getUserFromToken(token);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { id, question, answer, keywords, category, confidence_threshold, is_active } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Memory ID is required' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (question !== undefined) updateData.question = question.trim();
    if (answer !== undefined) updateData.answer = answer.trim();
    if (keywords !== undefined) updateData.keywords = keywords.map((k: string) => k.toLowerCase().trim());
    if (category !== undefined) updateData.category = category;
    if (confidence_threshold !== undefined) updateData.confidence_threshold = confidence_threshold;
    if (is_active !== undefined) updateData.is_active = is_active;
    updateData.updated_at = new Date().toISOString();

    const { data: memory, error } = await supabase
      .from('memory')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Memory update error:', error);
      return NextResponse.json(
        { error: 'Failed to update memory entry' },
        { status: 500 }
      );
    }

    return NextResponse.json({ memory });
  } catch (error) {
    console.error('Memory API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mantine/core": "^8.1.1", "@mantine/dates": "^8.1.1", "@mantine/dropzone": "^8.1.1", "@mantine/form": "^8.1.1", "@mantine/hooks": "^8.1.1", "@mantine/modals": "^8.1.1", "@mantine/notifications": "^8.1.1", "@supabase/supabase-js": "^2.50.0", "@tabler/icons-react": "^3.34.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}
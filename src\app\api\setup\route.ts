import { NextResponse } from 'next/server';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import { hashPassword } from '@/lib/auth';
import { config } from '@/lib/config';

export async function GET() {
  try {
    const results = {
      database: { status: 'checking...', details: '' },
      tables: { status: 'checking...', details: '' },
      admin_user: { status: 'checking...', details: '' },
      llm: { status: 'checking...', details: '' },
      message_endpoint: { status: 'checking...', details: '' },
    };

    // Test database connection
    try {
      const { error } = await supabase.from('products').select('count').limit(1);
      if (error) throw error;
      results.database = { status: 'OK', details: 'Database connection successful' };
    } catch (error) {
      results.database = { status: 'ERROR', details: `Database connection failed: ${error}` };
    }

    // Test table structure
    try {
      const tables = ['products', 'messages', 'responses', 'memory', 'admin_users'];
      const tableResults = [];
      
      for (const table of tables) {
        try {
          const { error } = await supabase.from(table).select('*').limit(1);
          if (error) throw error;
          tableResults.push(`${table}: OK`);
        } catch (error) {
          tableResults.push(`${table}: ERROR - ${error}`);
        }
      }
      
      results.tables = { 
        status: tableResults.every(r => r.includes('OK')) ? 'OK' : 'PARTIAL', 
        details: tableResults.join(', ') 
      };
    } catch (error) {
      results.tables = { status: 'ERROR', details: `Table check failed: ${error}` };
    }

    // Check/Create admin user
    try {
      const { data: existingUser } = await supabaseAdmin
        .from('admin_users')
        .select('username')
        .eq('username', 'admin')
        .single();

      if (!existingUser) {
        // Create admin user with correct password hash
        const passwordHash = await hashPassword('admin123');
        const { error } = await supabaseAdmin
          .from('admin_users')
          .insert({
            username: 'admin',
            password_hash: passwordHash,
            email: '<EMAIL>',
          });

        if (error) throw error;
        results.admin_user = { status: 'CREATED', details: 'Admin user created with username: admin, password: admin123' };
      } else {
        results.admin_user = { status: 'EXISTS', details: 'Admin user already exists' };
      }
    } catch (error) {
      results.admin_user = { status: 'ERROR', details: `Admin user setup failed: ${error}` };
    }

    // Test LLM configuration
    try {
      const llmConfig = config.llm;
      if (!llmConfig.apiKey) {
        results.llm = { status: 'NOT_CONFIGURED', details: `${llmConfig.provider} API key not set` };
      } else {
        results.llm = { 
          status: 'CONFIGURED', 
          details: `${llmConfig.provider} configured with model: ${llmConfig.model}` 
        };
      }
    } catch (error) {
      results.llm = { status: 'ERROR', details: `LLM config error: ${error}` };
    }

    // Test message endpoint
    try {
      const messageUrl = `${process.env.NODE_ENV === 'production' ? 'https' : 'http'}://${process.env.VERCEL_URL || 'localhost:3000'}/api/message`;

      // Test GET endpoint (documentation)
      const getResponse = await fetch(messageUrl);
      if (getResponse.ok) {
        const docData = await getResponse.json();
        if (docData.name && docData.name.includes('Message Processing API')) {
          results.message_endpoint = {
            status: 'OK',
            details: `Message endpoint available at ${messageUrl}`
          };
        } else {
          results.message_endpoint = {
            status: 'ERROR',
            details: 'Message endpoint returned unexpected response'
          };
        }
      } else {
        results.message_endpoint = {
          status: 'ERROR',
          details: `Message endpoint not accessible (${getResponse.status})`
        };
      }
    } catch (error) {
      results.message_endpoint = {
        status: 'ERROR',
        details: `Message endpoint test failed: ${error}`
      };
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      results,
      environment: process.env.NODE_ENV,
      jwt_secret_configured: !!process.env.JWT_SECRET,
    });
  } catch (error) {
    console.error('Setup check error:', error);
    return NextResponse.json(
      { error: 'Setup check failed', details: error },
      { status: 500 }
    );
  }
}

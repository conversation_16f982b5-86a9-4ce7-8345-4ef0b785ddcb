import { config, getSystemPrompt } from './config';

export interface LLMResponse {
  text: string;
  confidence: number;
  provider: string;
  model: string;
}

/**
 * Generate a response using the configured LLM provider
 * @param message - The user's message
 * @param context - Additional context (products, previous messages, etc.)
 * @returns LLM response
 */
export async function generateLLMResponse(
  message: string,
  context?: {
    products?: any[];
    previousMessages?: string[];
    userInfo?: any;
  }
): Promise<LLMResponse> {
  const llmConfig = config.llm;
  
  if (!llmConfig.apiKey) {
    throw new Error(`${llmConfig.provider} API key not configured`);
  }

  const systemPrompt = getSystemPrompt(config.replyTone);
  let contextPrompt = '';
  
  if (context?.products && context.products.length > 0) {
    contextPrompt += '\n\nAvailable Products:\n';
    context.products.forEach((product, index) => {
      contextPrompt += `${index + 1}. ${product.name} - $${product.price}`;
      if (product.description) {
        contextPrompt += ` - ${product.description}`;
      }
      contextPrompt += '\n';
    });
  }

  const fullPrompt = `${systemPrompt}${contextPrompt}\n\nCustomer Message: ${message}\n\nResponse:`;

  try {
    switch (llmConfig.provider) {
      case 'openai':
        return await callOpenAI(fullPrompt, llmConfig);
      case 'anthropic':
        return await callAnthropic(fullPrompt, llmConfig);
      case 'gemini':
        return await callGemini(fullPrompt, llmConfig);
      case 'openrouter':
        return await callOpenRouter(fullPrompt, llmConfig);
      default:
        throw new Error(`Unsupported LLM provider: ${llmConfig.provider}`);
    }
  } catch (error) {
    console.error('LLM generation error:', error);
    throw error;
  }
}

async function callOpenAI(prompt: string, llmConfig: any): Promise<LLMResponse> {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${llmConfig.apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: llmConfig.model || 'gpt-3.5-turbo',
      messages: [
        { role: 'user', content: prompt }
      ],
      max_tokens: config.maxResponseLength,
      temperature: 0.7,
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`OpenAI API error: ${error.error?.message || response.statusText}`);
  }

  const data = await response.json();
  const text = data.choices[0]?.message?.content?.trim() || '';
  
  return {
    text,
    confidence: 0.8,
    provider: 'openai',
    model: llmConfig.model || 'gpt-3.5-turbo',
  };
}

async function callAnthropic(prompt: string, llmConfig: any): Promise<LLMResponse> {
  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'x-api-key': llmConfig.apiKey,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01',
    },
    body: JSON.stringify({
      model: llmConfig.model || 'claude-3-sonnet-20240229',
      max_tokens: config.maxResponseLength,
      messages: [
        { role: 'user', content: prompt }
      ],
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Anthropic API error: ${error.error?.message || response.statusText}`);
  }

  const data = await response.json();
  const text = data.content[0]?.text?.trim() || '';
  
  return {
    text,
    confidence: 0.8,
    provider: 'anthropic',
    model: llmConfig.model || 'claude-3-sonnet-20240229',
  };
}

async function callGemini(prompt: string, llmConfig: any): Promise<LLMResponse> {
  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${llmConfig.model || 'gemini-pro'}:generateContent?key=${llmConfig.apiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      contents: [
        {
          parts: [
            { text: prompt }
          ]
        }
      ],
      generationConfig: {
        maxOutputTokens: config.maxResponseLength,
        temperature: 0.7,
      },
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Gemini API error: ${error.error?.message || response.statusText}`);
  }

  const data = await response.json();
  const text = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim() || '';
  
  return {
    text,
    confidence: 0.8,
    provider: 'gemini',
    model: llmConfig.model || 'gemini-pro',
  };
}

async function callOpenRouter(prompt: string, llmConfig: any): Promise<LLMResponse> {
  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${llmConfig.apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
      'X-Title': 'Product Management System',
    },
    body: JSON.stringify({
      model: llmConfig.model || 'google/gemini-pro',
      messages: [
        { role: 'user', content: prompt }
      ],
      max_tokens: config.maxResponseLength,
      temperature: 0.7,
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`OpenRouter API error: ${error.error?.message || response.statusText}`);
  }

  const data = await response.json();
  const text = data.choices[0]?.message?.content?.trim() || '';
  
  return {
    text,
    confidence: 0.8,
    provider: 'openrouter',
    model: llmConfig.model || 'google/gemini-pro',
  };
}

/**
 * Check if LLM is properly configured
 */
export function isLLMConfigured(): boolean {
  return !!config.llm.apiKey && config.enableAutoReply;
}

/**
 * Get available LLM providers
 */
export function getAvailableProviders(): string[] {
  return ['openai', 'anthropic', 'gemini', 'openrouter'];
}

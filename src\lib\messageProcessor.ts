import { supabase } from '@/lib/supabase';
import { MessageInsert, ResponseInsert } from '@/types/database';
import { generateLLMResponse, isLLMConfigured } from '@/lib/llm';
import { config } from '@/lib/config';

/**
 * Process an incoming message and generate an automated response
 * @param senderId - The ID of the message sender
 * @param messageText - The text content of the message
 * @param platform - The platform the message came from (default: 'third_party')
 * @param metadata - Additional metadata about the message
 * @returns The generated response text
 */
export async function processIncomingMessage(
  senderId: string,
  messageText: string,
  platform: string = 'third_party',
  metadata: Record<string, any> = {}
): Promise<string> {
  try {
    console.log(`Processing message from ${senderId}: "${messageText}"`);

    if (!messageText || messageText.trim() === '') {
      console.log('Skipping empty message');
      throw new Error('Message text is required');
    }

    // Save incoming message to database
    const messageData: MessageInsert = {
      sender_id: senderId,
      message_text: messageText.trim(),
      message_type: 'text',
      platform: platform,
      metadata: {
        timestamp: Date.now(),
        ...metadata,
      },
    };

    console.log('Saving message to database:', messageData);

    const { data: savedMessage, error: messageError } = await supabase
      .from('messages')
      .insert(messageData)
      .select()
      .single();

    if (messageError) {
      console.error('Error saving message:', messageError);
      throw new Error('Failed to save message to database');
    }

    console.log('Message saved successfully:', savedMessage.id);

    // Process the message and generate response
    const responseText = await generateResponse(savedMessage);
    
    return responseText;
  } catch (error) {
    console.error('Error processing incoming message:', error);
    throw error;
  }
}

/**
 * Generate an automated response for a message
 * @param message - The saved message object from the database
 * @returns The generated response text
 */
async function generateResponse(message: any): Promise<string> {
  try {
    const messageText = message.message_text.toLowerCase();
    let responseText = '';
    let confidenceScore = 0.5;

    // Check memory for matching responses
    const { data: memoryEntries } = await supabase
      .from('memory')
      .select('*')
      .eq('is_active', true);

    let bestMatch = null;
    let bestScore = 0;

    // Simple keyword matching algorithm
    for (const entry of memoryEntries || []) {
      const keywords = entry.keywords || [];
      let score = 0;
      
      for (const keyword of keywords) {
        if (messageText.includes(keyword.toLowerCase())) {
          score += 1;
        }
      }
      
      if (score > bestScore && score > 0) {
        bestScore = score;
        bestMatch = entry;
      }
    }

    if (bestMatch && bestScore >= 1) {
      responseText = bestMatch.answer;
      confidenceScore = Math.min(bestScore / bestMatch.keywords.length, 1.0);
    } else if (isLLMConfigured()) {
      // Try to generate response using LLM
      try {
        // Get product context for better responses
        const { data: products } = await supabase
          .from('products')
          .select('name, description, price, images')
          .limit(10);

        const llmResponse = await generateLLMResponse(message.message_text, {
          products: products || [],
        });

        responseText = llmResponse.text;
        confidenceScore = llmResponse.confidence;

        // Save the LLM response to memory for future use if confidence is high
        if (confidenceScore >= config.confidenceThreshold) {
          const keywords = extractKeywords(message.message_text);
          if (keywords.length > 0) {
            await supabase.from('memory').insert({
              question: message.message_text,
              answer: responseText,
              keywords,
              category: 'llm_generated',
              confidence_threshold: confidenceScore,
              is_active: true,
            });
          }
        }
      } catch (llmError) {
        console.error('LLM generation failed:', llmError);
        responseText = config.fallbackMessage;
        confidenceScore = 0.3;
      }
    } else {
      // Fallback response
      responseText = config.fallbackMessage;
      confidenceScore = 0.3;
    }

    // Save response to database
    const responseData: ResponseInsert = {
      message_id: message.id,
      response_text: responseText,
      response_type: 'auto',
      confidence_score: confidenceScore,
      llm_provider: bestMatch ? 'keyword_matching' : (isLLMConfigured() ? config.llm.provider : 'fallback'),
    };

    const { error: responseError } = await supabase
      .from('responses')
      .insert(responseData);

    if (responseError) {
      console.error('Error saving response:', responseError);
      // Don't throw here, we still want to return the response text
    }

    console.log(`Generated response for ${message.sender_id}: ${responseText}`);
    
    return responseText;
  } catch (error) {
    console.error('Error generating response:', error);
    // Return fallback response even if there's an error
    return 'Thank you for your message. Our team will get back to you soon!';
  }
}

/**
 * Check if a message looks like it's asking about product information
 * @param messageText - The message text to analyze
 * @returns True if the message appears to be asking about products
 */
export function isProductInquiry(messageText: string): boolean {
  const productKeywords = [
    'price', 'cost', 'how much', 'available', 'stock', 'buy', 'purchase',
    'product', 'item', 'size', 'color', 'specification', 'spec', 'feature'
  ];
  
  const lowerText = messageText.toLowerCase();
  return productKeywords.some(keyword => lowerText.includes(keyword));
}

/**
 * Extract product-related information from a message
 * @param messageText - The message text to analyze
 * @returns Extracted product information or null
 */
export function extractProductInfo(messageText: string): { productName?: string; inquiry?: string } | null {
  // This is a simple implementation - in a real app you might use NLP
  const lowerText = messageText.toLowerCase();

  if (isProductInquiry(lowerText)) {
    return {
      inquiry: messageText,
      // Could add more sophisticated product name extraction here
    };
  }

  return null;
}

/**
 * Extract keywords from a message for memory storage
 * @param messageText - The message text to analyze
 * @returns Array of keywords
 */
function extractKeywords(messageText: string): string[] {
  // Simple keyword extraction - you can make this more sophisticated
  const words = messageText.toLowerCase()
    .replace(/[^\w\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 2);

  // Remove common stop words
  const stopWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'what', 'when', 'with'];

  return words.filter(word => !stopWords.includes(word)).slice(0, 5);
}

'use client';

import { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  Text,
  Title,
  Group,
  Stack,
  Badge,
  Loader,
  Center,
  Alert,
  Button,
  Progress,
} from '@mantine/core';
import {
  IconPackage,
  IconMessages,
  IconMessageCircle,
  IconTrendingUp,
  IconAlertTriangle,
  IconCheck,
  IconX,
  IconRefresh,
  IconWifi,
  IconDatabase,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';

interface DashboardStats {
  totalProducts: number;
  totalMessages: number;
  totalResponses: number;
  recentMessages: any[];
}

interface SystemHealth {
  database: { status: string; details: string; responseTime?: number };
  llm: { status: string; details: string };
  message_endpoint: { status: string; details: string };
  memory_usage?: number;
  api_calls_today?: number;
  api_limit?: number;
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  dismissed?: boolean;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [healthLoading, setHealthLoading] = useState(false);

  useEffect(() => {
    loadDashboardStats();
    loadSystemHealth();
    // Set up periodic health checks
    const healthInterval = setInterval(loadSystemHealth, 60000); // Check every minute
    return () => clearInterval(healthInterval);
  }, []);

  const loadDashboardStats = async () => {
    try {
      // Load products count
      const productsResponse = await fetch('/api/products?limit=1');
      const productsData = await productsResponse.json();

      // Load messages count
      const messagesResponse = await fetch('/api/messages?limit=10');
      const messagesData = await messagesResponse.json();

      setStats({
        totalProducts: productsData.products?.length || 0,
        totalMessages: messagesData.messages?.length || 0,
        totalResponses: messagesData.messages?.reduce((acc: number, msg: any) =>
          acc + (msg.responses?.length || 0), 0) || 0,
        recentMessages: messagesData.messages?.slice(0, 5) || [],
      });
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
      addAlert('error', 'Data Loading Error', 'Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  const loadSystemHealth = async () => {
    try {
      setHealthLoading(true);
      const startTime = Date.now();

      // Load system setup status
      const setupResponse = await fetch('/api/setup');
      const setupData = await setupResponse.json();

      const responseTime = Date.now() - startTime;

      if (setupData.success) {
        const health: SystemHealth = {
          database: {
            ...setupData.results.database,
            responseTime
          },
          llm: setupData.results.llm,
          message_endpoint: setupData.results.message_endpoint,
        };

        setSystemHealth(health);
        checkForAlerts(health);
      } else {
        addAlert('error', 'System Check Failed', 'Unable to retrieve system status');
      }
    } catch (error) {
      console.error('Error loading system health:', error);
      addAlert('error', 'Connection Error', 'Failed to check system health');
    } finally {
      setHealthLoading(false);
    }
  };

  const addAlert = (type: SystemAlert['type'], title: string, message: string) => {
    const newAlert: SystemAlert = {
      id: Date.now().toString(),
      type,
      title,
      message,
      timestamp: new Date(),
    };

    setAlerts(prev => [newAlert, ...prev.slice(0, 4)]); // Keep only 5 most recent alerts

    // Show notification
    notifications.show({
      title,
      message,
      color: type === 'error' ? 'red' : type === 'warning' ? 'yellow' : 'blue',
    });
  };

  const checkForAlerts = (health: SystemHealth) => {
    // Check database response time
    if (health.database.responseTime && health.database.responseTime > 2000) {
      addAlert('warning', 'Slow Database', `Database response time: ${health.database.responseTime}ms`);
    }

    // Check LLM configuration
    if (health.llm.status === 'NOT_CONFIGURED') {
      addAlert('warning', 'LLM Not Configured', 'AI responses are disabled');
    }

    // Check message endpoint
    if (health.message_endpoint.status === 'ERROR') {
      addAlert('error', 'Message Endpoint Error', 'Third-party integrations may not work');
    }
  };

  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };

  if (loading) {
    return (
      <Center h={400}>
        <Loader size="lg" />
      </Center>
    );
  }

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'OK':
      case 'CONFIGURED':
        return <IconCheck size="1rem" color="green" />;
      case 'ERROR':
        return <IconX size="1rem" color="red" />;
      default:
        return <IconAlertTriangle size="1rem" color="orange" />;
    }
  };

  return (
    <Stack>
      <Group justify="space-between">
        <Title order={1}>Dashboard Overview</Title>
        <Button
          size="xs"
          variant="subtle"
          leftSection={<IconRefresh size="1rem" />}
          onClick={loadSystemHealth}
          loading={healthLoading}
        >
          Refresh Status
        </Button>
      </Group>

      {/* System Alerts */}
      {alerts.length > 0 && (
        <Stack gap="xs">
          {alerts.map((alert) => (
            <Alert
              key={alert.id}
              color={alert.type === 'error' ? 'red' : alert.type === 'warning' ? 'yellow' : 'blue'}
              title={alert.title}
              withCloseButton
              onClose={() => dismissAlert(alert.id)}
            >
              <Group justify="space-between">
                <Text size="sm">{alert.message}</Text>
                <Text size="xs" c="dimmed">
                  {alert.timestamp.toLocaleTimeString()}
                </Text>
              </Group>
            </Alert>
          ))}
        </Stack>
      )}

      {/* System Health Status */}
      {systemHealth && (
        <Card withBorder>
          <Group justify="space-between" mb="md">
            <Title order={4}>System Health</Title>
            <Badge color={
              systemHealth.database.status === 'OK' &&
              systemHealth.message_endpoint.status === 'OK' ? 'green' : 'yellow'
            }>
              {systemHealth.database.status === 'OK' &&
               systemHealth.message_endpoint.status === 'OK' ? 'Healthy' : 'Issues Detected'}
            </Badge>
          </Group>

          <Grid>
            <Grid.Col span={{ base: 12, sm: 4 }}>
              <Group>
                <IconDatabase size="1.2rem" />
                {getHealthIcon(systemHealth.database.status)}
                <div>
                  <Text size="sm" fw={500}>Database</Text>
                  <Text size="xs" c="dimmed">
                    {systemHealth.database.responseTime}ms response
                  </Text>
                </div>
              </Group>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 4 }}>
              <Group>
                <IconWifi size="1.2rem" />
                {getHealthIcon(systemHealth.message_endpoint.status)}
                <div>
                  <Text size="sm" fw={500}>Message API</Text>
                  <Text size="xs" c="dimmed">
                    {systemHealth.message_endpoint.status}
                  </Text>
                </div>
              </Group>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 4 }}>
              <Group>
                <IconMessageCircle size="1.2rem" />
                {getHealthIcon(systemHealth.llm.status)}
                <div>
                  <Text size="sm" fw={500}>LLM Provider</Text>
                  <Text size="xs" c="dimmed">
                    {systemHealth.llm.status}
                  </Text>
                </div>
              </Group>
            </Grid.Col>
          </Grid>
        </Card>
      )}

      <Grid>
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder>
            <Group justify="space-between">
              <div>
                <Text c="dimmed" size="sm" tt="uppercase" fw={700}>
                  Total Products
                </Text>
                <Text fw={700} size="xl">
                  {stats?.totalProducts || 0}
                </Text>
              </div>
              <IconPackage size="2rem" color="blue" />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder>
            <Group justify="space-between">
              <div>
                <Text c="dimmed" size="sm" tt="uppercase" fw={700}>
                  Total Messages
                </Text>
                <Text fw={700} size="xl">
                  {stats?.totalMessages || 0}
                </Text>
              </div>
              <IconMessages size="2rem" color="green" />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder>
            <Group justify="space-between">
              <div>
                <Text c="dimmed" size="sm" tt="uppercase" fw={700}>
                  Total Responses
                </Text>
                <Text fw={700} size="xl">
                  {stats?.totalResponses || 0}
                </Text>
              </div>
              <IconMessageCircle size="2rem" color="orange" />
            </Group>
          </Card>
        </Grid.Col>
      </Grid>

      <Card withBorder>
        <Title order={3} mb="md">Recent Messages</Title>
        {stats?.recentMessages.length === 0 ? (
          <Text c="dimmed">No messages yet</Text>
        ) : (
          <Stack>
            {stats?.recentMessages.map((message: any) => (
              <Group key={message.id} justify="space-between">
                <div>
                  <Text fw={500}>{message.sender_name || message.sender_id}</Text>
                  <Text size="sm" c="dimmed" truncate>
                    {message.message_text}
                  </Text>
                </div>
                <div>
                  <Badge
                    color={message.responses?.length > 0 ? 'green' : 'yellow'}
                    variant="light"
                  >
                    {message.responses?.length > 0 ? 'Responded' : 'Pending'}
                  </Badge>
                  <Text size="xs" c="dimmed">
                    {new Date(message.created_at).toLocaleDateString()}
                  </Text>
                </div>
              </Group>
            ))}
          </Stack>
        )}
      </Card>
    </Stack>
  );
}

import { NextRequest, NextResponse } from 'next/server';

// Test endpoint to simulate third-party platform messages
export async function POST(request: NextRequest) {
  try {
    const { message, sender_id } = await request.json();

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Create the payload for the generic message endpoint
    const messagePayload = {
      sender_id: sender_id || 'test_user_123',
      message: message
    };

    // Send the payload to our generic message endpoint
    const messageUrl = `${request.nextUrl.origin}/api/message`;
    const messageResponse = await fetch(messageUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(messagePayload),
    });

    const messageResult = await messageResponse.json();

    return NextResponse.json({
      success: true,
      message: 'Test message sent to message endpoint',
      request_payload: messagePayload,
      response: messageResult,
      status: messageResponse.status,
    });
  } catch (error) {
    console.error('Test message error:', error);
    return NextResponse.json(
      { error: 'Failed to send test message', details: error },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    instructions: {
      method: 'POST',
      body: {
        message: 'Your test message here',
        sender_id: 'optional_sender_id'
      },
      example: 'curl -X POST http://localhost:3000/api/test-message -H "Content-Type: application/json" -d \'{"message": "Hello, this is a test!"}\''
    }
  });
}

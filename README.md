# Product Management System

A full-stack Next.js 14 application with Mantine UI, Supabase, and third-party messaging integration for product management and customer service automation.

## Features

- 🛍️ **Product Management**: Full CRUD operations for product catalog
- 💬 **Message Handling**: Third-party messaging platform integration (Make.com, n8n, etc.)
- 🤖 **AI Responses**: Configurable LLM providers (Gemini, OpenRouter, OpenAI, Anthropic)
- 🔐 **Secure Authentication**: Session-based auth with JWT tokens
- 📊 **Admin Dashboard**: Comprehensive management interface
- 🎨 **Modern UI**: Built with Mantine UI components
- 📱 **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

- **Frontend**: Next.js 14 (App Router), TypeScript, Mantine UI
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: JWT with HTTP-only cookies
- **Styling**: Mantine UI components and CSS modules

## Quick Start

### 1. Clone and Install

```bash
git clone <your-repo-url>
cd nextjs
npm install
```

### 2. Set up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to SQL Editor and run the contents of `supabase.schema.sql`
3. Get your project URL and API keys from Settings > API

### 3. Configure Environment Variables

Copy `.env.local.example` to `.env.local` and fill in your values:

```bash
cp .env.local.example .env.local
```

Required variables:
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anon key
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
- `JWT_SECRET`: A secure random string for JWT signing
- `LLM_PROVIDER`: Choose from `gemini`, `openrouter`, `openai`, `anthropic`
- API key for your chosen LLM provider

### 4. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

### 5. Login to Admin Dashboard

- Navigate to `/login` or click "Admin Login" on the homepage
- Default credentials: `admin` / `admin123`
- Change these credentials in production!

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin dashboard pages
│   ├── api/               # API routes
│   └── login/             # Login page
├── components/            # Reusable React components
├── lib/                   # Utility functions and configurations
│   ├── auth.ts           # Authentication utilities
│   ├── config.ts         # LLM and app configuration
│   └── supabase.ts       # Supabase client setup
└── types/                 # TypeScript type definitions
    └── database.ts        # Database schema types
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Products
- `GET /api/products` - List products (with search)
- `POST /api/products` - Create product (admin only)
- `GET /api/products/[id]` - Get single product
- `PUT /api/products/[id]` - Update product (admin only)
- `DELETE /api/products/[id]` - Delete product (admin only)

### Messages & Responses
- `GET /api/messages` - List messages with responses (admin only)
- `POST /api/responses` - Create manual response (admin only)
- `POST /api/message` - Process incoming messages from third-party platforms
- `GET /api/message` - API documentation for message endpoint

## Database Schema

The application uses the following main tables:

- **products**: Product catalog with specs stored as JSONB
- **messages**: Incoming customer messages from various platforms
- **responses**: Bot and manual responses linked to messages
- **memory**: Stored Q&A pairs for bot training
- **admin_users**: Admin user accounts with hashed passwords

## Configuration

### LLM Providers

The app supports multiple LLM providers. Configure in your `.env.local`:

```bash
# For Gemini
LLM_PROVIDER=gemini
GEMINI_API_KEY=your_key_here

# For OpenRouter
LLM_PROVIDER=openrouter
OPENROUTER_API_KEY=your_key_here

# For OpenAI
LLM_PROVIDER=openai
OPENAI_API_KEY=your_key_here

# For Anthropic
LLM_PROVIDER=anthropic
ANTHROPIC_API_KEY=your_key_here
```

### Bot Configuration

Customize bot behavior:

```bash
REPLY_TONE=friendly          # professional, friendly, casual, formal
CONFIDENCE_THRESHOLD=0.8     # 0.0 to 1.0
MAX_RESPONSE_LENGTH=500      # Maximum characters
ENABLE_AUTO_REPLY=true       # Enable/disable auto responses
```

## Third-Party Integration Setup

The application now receives messages through a generic `/api/message` endpoint that works with platforms like Make.com, n8n, Zapier, and others.

### Integration Examples

#### Make.com
1. Create a new scenario
2. Add an HTTP module
3. Set method to POST
4. Set URL to `https://your-domain.com/api/message`
5. Set request body to:
   ```json
   {
     "sender_id": "{{sender_id}}",
     "message": "{{message_text}}"
   }
   ```

#### n8n
1. Add an HTTP Request node
2. Set method to POST
3. Set URL to `https://your-domain.com/api/message`
4. Set body to JSON with:
   ```json
   {
     "sender_id": "{{ $json.sender_id }}",
     "message": "{{ $json.message }}"
   }
   ```

#### API Response Format
The endpoint returns:
```json
{
  "reply": "Generated response text"
}
```

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Security Notes

- Change default admin credentials immediately
- Use strong JWT secrets in production
- Enable HTTPS in production
- Regularly update dependencies
- Review and audit API access logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

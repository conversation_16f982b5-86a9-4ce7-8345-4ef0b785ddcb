import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { getUserFromToken } from '@/lib/auth';
import { cookies } from 'next/headers';
import { ResponseInsert } from '@/types/database';

// POST /api/responses - Create manual response (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const cookieStore = await cookies();
    const token = cookieStore.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = await getUserFromToken(token);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { message_id, response_text } = body;

    if (!message_id || !response_text) {
      return NextResponse.json(
        { error: 'Message ID and response text are required' },
        { status: 400 }
      );
    }

    // Check if message exists
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .select('id')
      .eq('id', message_id)
      .single();

    if (messageError || !message) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    const responseData: ResponseInsert = {
      message_id,
      response_text,
      response_type: 'manual',
      created_by: user.username,
      sent_at: new Date().toISOString(),
    };

    const { data: response, error } = await supabase
      .from('responses')
      .insert(responseData)
      .select()
      .single();

    if (error) {
      console.error('Response creation error:', error);
      return NextResponse.json(
        { error: 'Failed to create response' },
        { status: 500 }
      );
    }

    return NextResponse.json({ response }, { status: 201 });
  } catch (error) {
    console.error('Create response API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Card,
  Text,
  Group,
  Badge,
  Button,
  Textarea,
  Modal,
  Loader,
  Center,
  Alert,
  Divider,
  ScrollArea,
} from '@mantine/core';
import {
  IconMessage,
  IconSend,
  IconAlertCircle,
  IconRefresh,
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { Message, Response } from '@/types/database';

interface MessageWithResponses extends Message {
  responses: Response[];
}

export default function MessagesPage() {
  const [messages, setMessages] = useState<MessageWithResponses[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMessage, setSelectedMessage] = useState<MessageWithResponses | null>(null);
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);
  const [sendingResponse, setSendingResponse] = useState(false);

  const responseForm = useForm({
    initialValues: {
      response_text: '',
    },
    validate: {
      response_text: (value) => (!value.trim() ? 'Response text is required' : null),
    },
  });

  useEffect(() => {
    loadMessages();
  }, []);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/messages');
      const data = await response.json();
      
      if (response.ok) {
        setMessages(data.messages || []);
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to load messages',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load messages',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleReplyToMessage = (message: MessageWithResponses) => {
    setSelectedMessage(message);
    responseForm.reset();
    openModal();
  };

  const handleSendResponse = async (values: typeof responseForm.values) => {
    if (!selectedMessage) return;

    setSendingResponse(true);

    try {
      const response = await fetch('/api/responses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message_id: selectedMessage.id,
          response_text: values.response_text,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        notifications.show({
          title: 'Success',
          message: 'Response sent successfully',
          color: 'green',
        });
        closeModal();
        loadMessages(); // Reload to show the new response
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to send response',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error sending response:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to send response',
        color: 'red',
      });
    } finally {
      setSendingResponse(false);
    }
  };

  const getResponseStatusBadge = (responses: Response[]) => {
    if (responses.length === 0) {
      return <Badge color="yellow" variant="light">No Response</Badge>;
    }

    const hasManualResponse = responses.some(r => r.response_type === 'manual');
    if (hasManualResponse) {
      return <Badge color="blue" variant="light">Manual Response</Badge>;
    }

    return <Badge color="green" variant="light">Auto Response</Badge>;
  };

  if (loading) {
    return (
      <Center h={400}>
        <Loader size="lg" />
      </Center>
    );
  }

  return (
    <Stack>
      <Group justify="space-between">
        <Title order={1}>Messages</Title>
        <Button
          leftSection={<IconRefresh size="1rem" />}
          onClick={loadMessages}
          loading={loading}
        >
          Refresh
        </Button>
      </Group>

      {messages.length === 0 ? (
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="No messages found"
          color="blue"
        >
          No customer messages have been received yet.
        </Alert>
      ) : (
        <Stack>
          {messages.map((message) => (
            <Card key={message.id} withBorder>
              <Stack>
                <Group justify="space-between">
                  <div>
                    <Text fw={500}>
                      {message.sender_name || `User ${message.sender_id}`}
                    </Text>
                    <Text size="sm" c="dimmed">
                      {new Date(message.created_at).toLocaleString()}
                    </Text>
                  </div>
                  <Group>
                    {getResponseStatusBadge(message.responses)}
                    <Badge variant="light" color="gray">
                      {message.platform}
                    </Badge>
                  </Group>
                </Group>

                <Text>{message.message_text}</Text>

                {message.responses.length > 0 && (
                  <>
                    <Divider />
                    <Text size="sm" fw={500} c="dimmed">
                      Responses:
                    </Text>
                    <ScrollArea.Autosize mah={200}>
                      <Stack gap="xs">
                        {message.responses.map((response) => (
                          <Card key={response.id} withBorder bg="gray.0">
                            <Group justify="space-between" mb="xs">
                              <Badge
                                size="sm"
                                color={response.response_type === 'manual' ? 'blue' : 'green'}
                              >
                                {response.response_type === 'manual' ? 'Manual' : 'Auto'}
                              </Badge>
                              <Text size="xs" c="dimmed">
                                {new Date(response.created_at).toLocaleString()}
                              </Text>
                            </Group>
                            <Text size="sm">{response.response_text}</Text>
                            {response.created_by && (
                              <Text size="xs" c="dimmed" mt="xs">
                                By: {response.created_by}
                              </Text>
                            )}
                          </Card>
                        ))}
                      </Stack>
                    </ScrollArea.Autosize>
                  </>
                )}

                <Group justify="flex-end">
                  <Button
                    size="sm"
                    leftSection={<IconMessage size="1rem" />}
                    onClick={() => handleReplyToMessage(message)}
                  >
                    Send Manual Response
                  </Button>
                </Group>
              </Stack>
            </Card>
          ))}
        </Stack>
      )}

      <Modal
        opened={modalOpened}
        onClose={closeModal}
        title="Send Manual Response"
        size="md"
      >
        {selectedMessage && (
          <Stack>
            <Card withBorder bg="gray.0">
              <Text size="sm" fw={500} mb="xs">
                Original Message:
              </Text>
              <Text size="sm">{selectedMessage.message_text}</Text>
              <Text size="xs" c="dimmed" mt="xs">
                From: {selectedMessage.sender_name || selectedMessage.sender_id}
              </Text>
            </Card>

            <form onSubmit={responseForm.onSubmit(handleSendResponse)}>
              <Stack>
                <Textarea
                  label="Your Response"
                  placeholder="Type your response here..."
                  required
                  rows={4}
                  {...responseForm.getInputProps('response_text')}
                />

                <Group justify="flex-end">
                  <Button variant="subtle" onClick={closeModal}>
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    leftSection={<IconSend size="1rem" />}
                    loading={sendingResponse}
                  >
                    Send Response
                  </Button>
                </Group>
              </Stack>
            </form>
          </Stack>
        )}
      </Modal>
    </Stack>
  );
}

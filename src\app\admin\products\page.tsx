'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>ack,
  Title,
  Button,
  Table,
  Group,
  ActionIcon,
  Text,
  Badge,
  TextInput,
  Modal,
  Loader,
  Center,
  Alert,
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconSearch,
  IconAlertCircle,
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { modals } from '@mantine/modals';
import { Product } from '@/types/database';
import ProductForm from '@/components/ProductForm';

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async (search?: string) => {
    try {
      setLoading(true);
      const url = search 
        ? `/api/products?search=${encodeURIComponent(search)}`
        : '/api/products';
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (response.ok) {
        setProducts(data.products || []);
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to load products',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error loading products:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load products',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    loadProducts(searchQuery);
  };

  const handleAddProduct = () => {
    setSelectedProduct(null);
    openModal();
  };

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    openModal();
  };

  const handleDeleteProduct = (product: Product) => {
    modals.openConfirmModal({
      title: 'Delete Product',
      children: (
        <Text size="sm">
          Are you sure you want to delete "{product.name}"? This action cannot be undone.
        </Text>
      ),
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: () => deleteProduct(product.id),
    });
  };

  const deleteProduct = async (productId: string) => {
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        notifications.show({
          title: 'Success',
          message: 'Product deleted successfully',
          color: 'green',
        });
        loadProducts();
      } else {
        const data = await response.json();
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to delete product',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to delete product',
        color: 'red',
      });
    }
  };

  const handleProductSaved = () => {
    closeModal();
    loadProducts();
  };

  if (loading && products.length === 0) {
    return (
      <Center h={400}>
        <Loader size="lg" />
      </Center>
    );
  }

  return (
    <Stack>
      <Group justify="space-between">
        <Title order={1}>Products</Title>
        <Button leftSection={<IconPlus size="1rem" />} onClick={handleAddProduct}>
          Add Product
        </Button>
      </Group>

      <Group>
        <TextInput
          placeholder="Search products..."
          leftSection={<IconSearch size="1rem" />}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.currentTarget.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          style={{ flex: 1 }}
        />
        <Button onClick={handleSearch}>Search</Button>
      </Group>

      {products.length === 0 ? (
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="No products found"
          color="blue"
        >
          {searchQuery 
            ? 'No products match your search criteria.'
            : 'No products have been added yet. Click "Add Product" to get started.'
          }
        </Alert>
      ) : (
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Name</Table.Th>
              <Table.Th>Description</Table.Th>
              <Table.Th>Price</Table.Th>
              <Table.Th>Images</Table.Th>
              <Table.Th>Created</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {products.map((product) => (
              <Table.Tr key={product.id}>
                <Table.Td>
                  <Text fw={500}>{product.name}</Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm" c="dimmed" truncate>
                    {product.description || 'No description'}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Badge variant="light" color="green">
                    ${product.price.toFixed(2)}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Badge variant="light" color="blue">
                    {product.images?.length || 0} image{(product.images?.length || 0) !== 1 ? 's' : ''}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Text size="sm" c="dimmed">
                    {new Date(product.created_at).toLocaleDateString()}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Group gap="xs">
                    <ActionIcon
                      variant="subtle"
                      color="blue"
                      onClick={() => handleEditProduct(product)}
                    >
                      <IconEdit size="1rem" />
                    </ActionIcon>
                    <ActionIcon
                      variant="subtle"
                      color="red"
                      onClick={() => handleDeleteProduct(product)}
                    >
                      <IconTrash size="1rem" />
                    </ActionIcon>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      )}

      <Modal
        opened={modalOpened}
        onClose={closeModal}
        title={selectedProduct ? 'Edit Product' : 'Add Product'}
        size="lg"
      >
        <ProductForm
          product={selectedProduct}
          onSave={handleProductSaved}
          onCancel={closeModal}
        />
      </Modal>
    </Stack>
  );
}

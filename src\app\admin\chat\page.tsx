'use client';

import { useState, useRef, useEffect } from 'react';
import {
  Container,
  Paper,
  Stack,
  Group,
  TextInput,
  Button,
  Text,
  ScrollArea,
  Card,
  Badge,
  Textarea,
  Alert,
  ActionIcon,
  Tooltip,
  Divider,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconSend,
  IconUser,
  IconRobot,
  IconEdit,
  IconCheck,
  IconX,
  IconAlertCircle,
  IconBrain,
} from '@tabler/icons-react';

interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  confidence?: number;
  corrected?: boolean;
}

export default function ChatPage() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [correcting, setCorrecting] = useState<string | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const messageForm = useForm({
    initialValues: {
      message: '',
    },
    validate: {
      message: (value) => (!value.trim() ? 'Message is required' : null),
    },
  });

  const correctionForm = useForm({
    initialValues: {
      correction: '',
    },
    validate: {
      correction: (value) => (!value.trim() ? 'Correction is required' : null),
    },
  });

  useEffect(() => {
    // Scroll to bottom when new messages are added
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const sendMessage = async (values: typeof messageForm.values) => {
    if (!values.message.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: values.message.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    messageForm.reset();
    setLoading(true);

    try {
      // Send message to the API
      const response = await fetch('/api/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sender_id: 'admin_chat_test',
          message: userMessage.content,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        const botMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'bot',
          content: data.reply,
          timestamp: new Date(),
          confidence: 0.8, // You can extract this from the API response if available
        };

        setMessages(prev => [...prev, botMessage]);
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to get response',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Chat error:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to send message',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const startCorrection = (messageId: string) => {
    setCorrecting(messageId);
    const message = messages.find(m => m.id === messageId);
    if (message) {
      correctionForm.setFieldValue('correction', message.content);
    }
  };

  const cancelCorrection = () => {
    setCorrecting(null);
    correctionForm.reset();
  };

  const submitCorrection = async (messageId: string, values: typeof correctionForm.values) => {
    if (!values.correction.trim()) return;

    try {
      // Find the user message that prompted this bot response
      const messageIndex = messages.findIndex(m => m.id === messageId);
      const userMessage = messageIndex > 0 ? messages[messageIndex - 1] : null;

      if (!userMessage || userMessage.type !== 'user') {
        notifications.show({
          title: 'Error',
          message: 'Could not find the original question',
          color: 'red',
        });
        return;
      }

      // Save the correction to memory
      const response = await fetch('/api/admin/memory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: userMessage.content,
          answer: values.correction.trim(),
          keywords: extractKeywords(userMessage.content),
          category: 'admin_correction',
        }),
      });

      if (response.ok) {
        // Update the message to show it's been corrected
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, content: values.correction.trim(), corrected: true }
            : msg
        ));

        notifications.show({
          title: 'Success',
          message: 'Correction saved! The bot will use this response for similar questions.',
          color: 'green',
        });

        setCorrecting(null);
        correctionForm.reset();
      } else {
        const data = await response.json();
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to save correction',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Correction error:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to save correction',
        color: 'red',
      });
    }
  };

  const extractKeywords = (text: string): string[] => {
    // Simple keyword extraction - you can make this more sophisticated
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    // Remove common stop words
    const stopWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'what', 'when', 'with'];
    
    return words.filter(word => !stopWords.includes(word)).slice(0, 5);
  };

  const clearChat = () => {
    setMessages([]);
    setCorrecting(null);
    correctionForm.reset();
    messageForm.reset();
  };

  return (
    <Container size="lg">
      <Stack>
        <Group justify="space-between">
          <div>
            <Text size="xl" fw={600}>Chat Interface</Text>
            <Text size="sm" c="dimmed">
              Test the chatbot responses and provide corrections to improve accuracy
            </Text>
          </div>
          <Button variant="subtle" onClick={clearChat} disabled={messages.length === 0}>
            Clear Chat
          </Button>
        </Group>

        <Alert color="blue" icon={<IconBrain size="1rem" />}>
          <Text fw={500} mb="xs">How to use this chat interface:</Text>
          <Text size="sm">
            1. Ask questions as if you were a customer<br/>
            2. Review the bot's responses<br/>
            3. Click the edit icon to correct wrong answers<br/>
            4. Your corrections will be saved and used for future similar questions
          </Text>
        </Alert>

        {/* Chat Messages */}
        <Paper withBorder style={{ height: '500px', display: 'flex', flexDirection: 'column' }}>
          <ScrollArea flex={1} p="md" ref={scrollAreaRef}>
            {messages.length === 0 ? (
              <Text c="dimmed" ta="center" mt="xl">
                Start a conversation to test the chatbot...
              </Text>
            ) : (
              <Stack gap="md">
                {messages.map((message) => (
                  <Card
                    key={message.id}
                    withBorder
                    p="sm"
                    style={{
                      alignSelf: message.type === 'user' ? 'flex-end' : 'flex-start',
                      maxWidth: '80%',
                      backgroundColor: message.type === 'user' ? '#e3f2fd' : '#f5f5f5',
                    }}
                  >
                    <Group justify="space-between" mb="xs">
                      <Group gap="xs">
                        {message.type === 'user' ? (
                          <IconUser size="1rem" color="#1976d2" />
                        ) : (
                          <IconRobot size="1rem" color="#388e3c" />
                        )}
                        <Text size="sm" fw={500}>
                          {message.type === 'user' ? 'You' : 'Bot'}
                        </Text>
                        {message.confidence && (
                          <Badge size="xs" color={message.confidence > 0.7 ? 'green' : 'yellow'}>
                            {Math.round(message.confidence * 100)}% confident
                          </Badge>
                        )}
                        {message.corrected && (
                          <Badge size="xs" color="blue">
                            Corrected
                          </Badge>
                        )}
                      </Group>
                      
                      {message.type === 'bot' && (
                        <Group gap="xs">
                          <Tooltip label="Correct this response">
                            <ActionIcon
                              size="sm"
                              variant="subtle"
                              onClick={() => startCorrection(message.id)}
                              disabled={correcting === message.id}
                            >
                              <IconEdit size="0.8rem" />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      )}
                    </Group>

                    {correcting === message.id ? (
                      <form onSubmit={correctionForm.onSubmit((values) => submitCorrection(message.id, values))}>
                        <Stack gap="xs">
                          <Textarea
                            placeholder="Enter the correct response..."
                            autosize
                            minRows={2}
                            {...correctionForm.getInputProps('correction')}
                          />
                          <Group gap="xs">
                            <Button
                              size="xs"
                              type="submit"
                              leftSection={<IconCheck size="0.8rem" />}
                            >
                              Save Correction
                            </Button>
                            <Button
                              size="xs"
                              variant="subtle"
                              onClick={cancelCorrection}
                              leftSection={<IconX size="0.8rem" />}
                            >
                              Cancel
                            </Button>
                          </Group>
                        </Stack>
                      </form>
                    ) : (
                      <Text size="sm">{message.content}</Text>
                    )}

                    <Text size="xs" c="dimmed" mt="xs">
                      {message.timestamp.toLocaleTimeString()}
                    </Text>
                  </Card>
                ))}
              </Stack>
            )}
          </ScrollArea>

          <Divider />

          {/* Message Input */}
          <form onSubmit={messageForm.onSubmit(sendMessage)}>
            <Group p="md" gap="xs">
              <TextInput
                placeholder="Type your message..."
                style={{ flex: 1 }}
                disabled={loading}
                {...messageForm.getInputProps('message')}
              />
              <Button
                type="submit"
                loading={loading}
                leftSection={<IconSend size="1rem" />}
                disabled={!messageForm.values.message.trim()}
              >
                Send
              </Button>
            </Group>
          </form>
        </Paper>
      </Stack>
    </Container>
  );
}

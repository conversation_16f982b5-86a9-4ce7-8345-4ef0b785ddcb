-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    images JSONB DEFAULT '[]'::jsonb, -- Array of image URLs
    specs JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Migration: Add images column and migrate existing image_url data
DO $$
BEGIN
    -- Add images column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'images') THEN
        ALTER TABLE products ADD COLUMN images JSONB DEFAULT '[]'::jsonb;
    END IF;

    -- Migrate existing image_url data to images array if image_url column exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'image_url') THEN
        UPDATE products
        SET images = CASE
            WHEN image_url IS NOT NULL AND image_url != ''
            THEN jsonb_build_array(image_url)
            ELSE '[]'::jsonb
        END
        WHERE images = '[]'::jsonb OR images IS NULL;

        -- Drop the old image_url column
        ALTER TABLE products DROP COLUMN IF EXISTS image_url;
    END IF;
END $$;

-- Create messages table for incoming customer messages
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    sender_id VARCHAR(255) NOT NULL, -- Facebook user ID
    sender_name VARCHAR(255),
    message_text TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text', -- text, image, etc.
    platform VARCHAR(50) DEFAULT 'facebook', -- facebook, whatsapp, etc.
    metadata JSONB, -- store additional message data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create responses table for bot/manual replies
CREATE TABLE IF NOT EXISTS responses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    message_id UUID REFERENCES messages(id) ON DELETE CASCADE,
    response_text TEXT NOT NULL,
    response_type VARCHAR(50) DEFAULT 'auto', -- auto, manual
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    llm_provider VARCHAR(100), -- gemini, openrouter, etc.
    created_by VARCHAR(255), -- admin user who created manual response
    sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create memory table for stored questions and answers
CREATE TABLE IF NOT EXISTS memory (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    keywords TEXT[], -- array of keywords for matching
    category VARCHAR(100),
    confidence_threshold DECIMAL(3,2) DEFAULT 0.80,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin_users table for authentication
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_responses_message_id ON responses(message_id);
CREATE INDEX IF NOT EXISTS idx_responses_created_at ON responses(created_at);
CREATE INDEX IF NOT EXISTS idx_memory_keywords ON memory USING GIN(keywords);
CREATE INDEX IF NOT EXISTS idx_memory_category ON memory(category);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_memory_updated_at BEFORE UPDATE ON memory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data
-- Note: Admin user will be created automatically via the /api/setup endpoint
-- Default credentials: admin / admin123 (change in production!)

-- You can also manually insert an admin user with:
-- INSERT INTO admin_users (username, password_hash, email) VALUES
-- ('admin', '$2b$12$hash_generated_by_bcrypt', '<EMAIL>')
-- ON CONFLICT (username) DO NOTHING;

INSERT INTO products (name, description, price, images, specs) VALUES
('Sample Product 1', 'This is a sample product description', 99.99, '["https://via.placeholder.com/300", "https://via.placeholder.com/300x200"]'::jsonb, '{"color": "blue", "size": "medium"}'),
('Sample Product 2', 'Another sample product', 149.99, '["https://via.placeholder.com/300"]'::jsonb, '{"color": "red", "size": "large"}')
ON CONFLICT DO NOTHING;

INSERT INTO memory (question, answer, keywords, category, confidence_threshold, is_active) VALUES
('What are your business hours?', 'We are open Monday to Friday, 9 AM to 6 PM EST.', ARRAY['hours', 'open', 'time', 'business'], 'general', 0.9, true),
('How can I contact support?', 'You can reach our support <NAME_EMAIL> or through this chat.', ARRAY['support', 'contact', 'help'], 'support', 0.9, true),
('What is your return policy?', 'We offer a 30-day return policy for all products in original condition.', ARRAY['return', 'refund', 'policy'], 'policy', 0.9, true),
('What products do you have?', 'We have a variety of products including electronics, clothing, and home goods. You can browse our full catalog on our website.', ARRAY['products', 'catalog', 'what', 'have'], 'general', 0.8, true),
('How much does shipping cost?', 'Shipping costs vary based on location and order size. Standard shipping is $5.99 for orders under $50, and free for orders over $50.', ARRAY['shipping', 'cost', 'delivery', 'price'], 'shipping', 0.9, true),
('Do you offer discounts?', 'Yes! We offer various discounts including first-time customer discounts, bulk order discounts, and seasonal sales. Sign up for our newsletter to stay updated.', ARRAY['discount', 'sale', 'coupon', 'offer'], 'pricing', 0.8, true)
ON CONFLICT DO NOTHING;

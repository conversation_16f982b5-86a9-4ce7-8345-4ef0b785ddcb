import { NextRequest, NextResponse } from 'next/server';

// Test endpoint for third-party message integration
export async function GET(request: NextRequest) {
  try {
    // Return test information for third-party integration
    return NextResponse.json({
      message_endpoint: `${request.nextUrl.origin}/api/message`,
      test_endpoint: `${request.nextUrl.origin}/api/test-message`,
      documentation: `${request.nextUrl.origin}/api/message`,
      instructions: {
        step1: 'Configure your third-party platform (Make.com, n8n, etc.)',
        step2: 'Set the webhook URL to point to /api/message',
        step3: 'Send POST requests with { "sender_id": "user123", "message": "text" }',
        step4: 'Use /api/test-message to test the integration',
      },
      example_request: {
        method: 'POST',
        url: `${request.nextUrl.origin}/api/message`,
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          sender_id: 'user123',
          message: 'Hello, how much does this cost?'
        }
      },
      example_response: {
        reply: 'Thank you for your inquiry! Please let me know which specific product you\'re interested in.'
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Test endpoint failed', details: error },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Forward the test request to the actual message endpoint
    const messageUrl = `${request.nextUrl.origin}/api/message`;
    const messageResponse = await fetch(messageUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const messageResult = await messageResponse.json();

    return NextResponse.json({
      message: 'Test request forwarded to message endpoint',
      original_request: body,
      message_endpoint_response: messageResult,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Test endpoint POST failed', details: error },
      { status: 500 }
    );
  }
}

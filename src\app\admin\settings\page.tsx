'use client';

import { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Card,
  Stack,
  Group,
  Text,
  Button,
  TextInput,
  PasswordInput,
  Table,
  Badge,
  Modal,
  Loader,
  Alert,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { IconPlus, IconKey, IconUsers, IconAlertCircle } from '@tabler/icons-react';

interface AdminUser {
  id: string;
  username: string;
  email: string | null;
  is_active: boolean;
  last_login: string | null;
  created_at: string;
}

export default function AdminSettingsPage() {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [changingPassword, setChangingPassword] = useState(false);
  const [creatingUser, setCreatingUser] = useState(false);
  
  const [passwordModalOpened, { open: openPasswordModal, close: closePasswordModal }] = useDisclosure(false);
  const [userModalOpened, { open: openUserModal, close: closeUserModal }] = useDisclosure(false);

  const passwordForm = useForm({
    initialValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    validate: {
      currentPassword: (value) => (!value ? 'Current password is required' : null),
      newPassword: (value) => (value.length < 6 ? 'Password must be at least 6 characters' : null),
      confirmPassword: (value, values) => 
        value !== values.newPassword ? 'Passwords do not match' : null,
    },
  });

  const userForm = useForm({
    initialValues: {
      username: '',
      password: '',
      email: '',
    },
    validate: {
      username: (value) => (value.length < 3 ? 'Username must be at least 3 characters' : null),
      password: (value) => (value.length < 6 ? 'Password must be at least 6 characters' : null),
      email: (value) => (value && !/^\S+@\S+$/.test(value) ? 'Invalid email format' : null),
    },
  });

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/users');
      const data = await response.json();
      
      if (response.ok) {
        setUsers(data.users || []);
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to load users',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error loading users:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load users',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleChangePassword = async (values: typeof passwordForm.values) => {
    try {
      setChangingPassword(true);
      const response = await fetch('/api/admin/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (response.ok) {
        notifications.show({
          title: 'Success',
          message: 'Password changed successfully',
          color: 'green',
        });
        passwordForm.reset();
        closePasswordModal();
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to change password',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error changing password:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to change password',
        color: 'red',
      });
    } finally {
      setChangingPassword(false);
    }
  };

  const handleCreateUser = async (values: typeof userForm.values) => {
    try {
      setCreatingUser(true);
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (response.ok) {
        notifications.show({
          title: 'Success',
          message: 'Admin user created successfully',
          color: 'green',
        });
        userForm.reset();
        closeUserModal();
        loadUsers(); // Reload the users list
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to create user',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error creating user:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to create user',
        color: 'red',
      });
    } finally {
      setCreatingUser(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Container size="lg">
      <Stack gap="xl">
        <Title order={2}>Admin Settings</Title>

        {/* Password Change Section */}
        <Card withBorder>
          <Stack>
            <Group justify="space-between">
              <div>
                <Text fw={500} size="lg">Change Password</Text>
                <Text size="sm" c="dimmed">Update your admin account password</Text>
              </div>
              <Button
                leftSection={<IconKey size="1rem" />}
                onClick={openPasswordModal}
              >
                Change Password
              </Button>
            </Group>
          </Stack>
        </Card>

        {/* Admin Users Section */}
        <Card withBorder>
          <Stack>
            <Group justify="space-between">
              <div>
                <Text fw={500} size="lg">Admin Users</Text>
                <Text size="sm" c="dimmed">Manage admin user accounts</Text>
              </div>
              <Button
                leftSection={<IconPlus size="1rem" />}
                onClick={openUserModal}
              >
                Add Admin User
              </Button>
            </Group>

            {loading ? (
              <Loader />
            ) : (
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Username</Table.Th>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Last Login</Table.Th>
                    <Table.Th>Created</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {users.map((user) => (
                    <Table.Tr key={user.id}>
                      <Table.Td>{user.username}</Table.Td>
                      <Table.Td>{user.email || '-'}</Table.Td>
                      <Table.Td>
                        <Badge color={user.is_active ? 'green' : 'red'}>
                          {user.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </Table.Td>
                      <Table.Td>{formatDate(user.last_login)}</Table.Td>
                      <Table.Td>{formatDate(user.created_at)}</Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            )}
          </Stack>
        </Card>

        {/* Security Notice */}
        <Alert color="blue" icon={<IconAlertCircle size="1rem" />}>
          <Text fw={500}>Security Notice</Text>
          <Text size="sm">
            • Always use strong passwords with at least 6 characters
            • Regularly update passwords for better security
            • Only create admin accounts for trusted users
            • Monitor the admin users list regularly
          </Text>
        </Alert>
      </Stack>

      {/* Change Password Modal */}
      <Modal
        opened={passwordModalOpened}
        onClose={closePasswordModal}
        title="Change Password"
        size="md"
      >
        <form onSubmit={passwordForm.onSubmit(handleChangePassword)}>
          <Stack>
            <PasswordInput
              label="Current Password"
              placeholder="Enter your current password"
              required
              {...passwordForm.getInputProps('currentPassword')}
            />
            <PasswordInput
              label="New Password"
              placeholder="Enter new password"
              required
              {...passwordForm.getInputProps('newPassword')}
            />
            <PasswordInput
              label="Confirm New Password"
              placeholder="Confirm new password"
              required
              {...passwordForm.getInputProps('confirmPassword')}
            />
            <Group justify="flex-end">
              <Button variant="subtle" onClick={closePasswordModal}>
                Cancel
              </Button>
              <Button type="submit" loading={changingPassword}>
                Change Password
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Create User Modal */}
      <Modal
        opened={userModalOpened}
        onClose={closeUserModal}
        title="Create Admin User"
        size="md"
      >
        <form onSubmit={userForm.onSubmit(handleCreateUser)}>
          <Stack>
            <TextInput
              label="Username"
              placeholder="Enter username"
              required
              {...userForm.getInputProps('username')}
            />
            <PasswordInput
              label="Password"
              placeholder="Enter password"
              required
              {...userForm.getInputProps('password')}
            />
            <TextInput
              label="Email (Optional)"
              placeholder="Enter email address"
              {...userForm.getInputProps('email')}
            />
            <Group justify="flex-end">
              <Button variant="subtle" onClick={closeUserModal}>
                Cancel
              </Button>
              <Button type="submit" loading={creatingUser}>
                Create User
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Container>
  );
}

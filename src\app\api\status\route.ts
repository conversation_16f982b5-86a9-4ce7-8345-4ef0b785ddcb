import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    const status = {
      timestamp: new Date().toISOString(),
      database: { connected: false, details: '' },
      tables: { all_exist: false, details: [] as string[] },
      messages: { count: 0, recent: [] as any[] },
      responses: { count: 0, recent: [] as any[] },
      memory: { count: 0, entries: [] as any[] },
      admin_users: { count: 0, exists: false },
      environment: {
        node_env: process.env.NODE_ENV,
        jwt_secret: !!process.env.JWT_SECRET,
        supabase_url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        supabase_anon: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        supabase_service: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
        llm_provider: process.env.LLM_PROVIDER || 'not_set',
        llm_configured: !!(process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY || process.env.OPENROUTER_API_KEY),
      }
    };

    // Test database connection
    try {
      const { error } = await supabase.from('products').select('count').limit(1);
      if (error) throw error;
      status.database = { connected: true, details: 'Connection successful' };
    } catch (error) {
      status.database = { connected: false, details: `Connection failed: ${error}` };
    }

    // Check all tables
    const tables = ['products', 'messages', 'responses', 'memory', 'admin_users'];
    const tableStatus = [];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('count').limit(1);
        if (error) throw error;
        tableStatus.push(`${table}: ✓`);
      } catch (error) {
        tableStatus.push(`${table}: ✗ (${error})`);
      }
    }
    
    status.tables = {
      all_exist: tableStatus.every(t => t.includes('✓')),
      details: tableStatus
    };

    // Get messages count and recent messages
    try {
      const { data: messages, error } = await supabase
        .from('messages')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (!error) {
        status.messages = {
          count: messages?.length || 0,
          recent: messages || []
        };
      }
    } catch (error) {
      status.messages = { count: 0, recent: [] };
    }

    // Get responses count and recent responses
    try {
      const { data: responses, error } = await supabase
        .from('responses')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (!error) {
        status.responses = {
          count: responses?.length || 0,
          recent: responses || []
        };
      }
    } catch (error) {
      status.responses = { count: 0, recent: [] };
    }

    // Get memory entries
    try {
      const { data: memory, error } = await supabase
        .from('memory')
        .select('*')
        .eq('is_active', true);
      
      if (!error) {
        status.memory = {
          count: memory?.length || 0,
          entries: memory || []
        };
      }
    } catch (error) {
      status.memory = { count: 0, entries: [] };
    }

    // Check admin users
    try {
      const { data: users, error } = await supabase
        .from('admin_users')
        .select('username')
        .eq('is_active', true);
      
      if (!error) {
        status.admin_users = {
          count: users?.length || 0,
          exists: users?.some(u => u.username === 'admin') || false
        };
      }
    } catch (error) {
      status.admin_users = { count: 0, exists: false };
    }

    return NextResponse.json(status);
  } catch (error) {
    console.error('Status check error:', error);
    return NextResponse.json(
      { error: 'Status check failed', details: error },
      { status: 500 }
    );
  }
}

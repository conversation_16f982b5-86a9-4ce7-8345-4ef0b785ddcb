import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { supabase } from './supabase';
import { AdminUser } from '@/types/database';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = '7d';

export interface AuthUser {
  id: string;
  username: string;
  email: string | null;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

// Hash password for storage
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// Verify password against hash
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

// Generate JWT token
export function generateToken(user: AuthUser): string {
  return jwt.sign(
    {
      id: user.id,
      username: user.username,
      email: user.email,
    },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );
}

// Verify JWT token
export function verifyToken(token: string): AuthUser | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as AuthUser;
    return decoded;
  } catch (error) {
    return null;
  }
}

// Authenticate user with username and password
export async function authenticateUser(credentials: LoginCredentials): Promise<{
  success: boolean;
  user?: AuthUser;
  token?: string;
  error?: string;
}> {
  try {
    const { data: user, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', credentials.username)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      return {
        success: false,
        error: 'Invalid username or password',
      };
    }

    const isValidPassword = await verifyPassword(credentials.password, user.password_hash);

    if (!isValidPassword) {
      return {
        success: false,
        error: 'Invalid username or password',
      };
    }

    // Update last login
    await supabase
      .from('admin_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id);

    const authUser: AuthUser = {
      id: user.id,
      username: user.username,
      email: user.email,
    };

    const token = generateToken(authUser);

    return {
      success: true,
      user: authUser,
      token,
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      error: 'Authentication failed',
    };
  }
}

// Get user from token
export async function getUserFromToken(token: string): Promise<AuthUser | null> {
  const decoded = verifyToken(token);
  if (!decoded) return null;

  try {
    const { data: user, error } = await supabase
      .from('admin_users')
      .select('id, username, email')
      .eq('id', decoded.id)
      .eq('is_active', true)
      .single();

    if (error || !user) return null;

    return {
      id: user.id,
      username: user.username,
      email: user.email,
    };
  } catch (error) {
    console.error('Get user from token error:', error);
    return null;
  }
}

// Create new admin user (for setup/registration)
export async function createAdminUser(
  username: string,
  password: string,
  email?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const passwordHash = await hashPassword(password);

    const { error } = await supabase
      .from('admin_users')
      .insert({
        username,
        password_hash: passwordHash,
        email,
      });

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return {
          success: false,
          error: 'Username already exists',
        };
      }
      return {
        success: false,
        error: 'Failed to create user',
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Create admin user error:', error);
    return {
      success: false,
      error: 'Failed to create user',
    };
  }
}

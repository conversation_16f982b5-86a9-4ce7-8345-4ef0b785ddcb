'use client';

import { useState } from 'react';
import {
  Stack,
  TextInput,
  Textarea,
  NumberInput,
  Button,
  Group,
  JsonInput,
  ActionIcon,
  Text,
  Card,
  Image,
  Alert,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconPlus, IconTrash, IconPhoto } from '@tabler/icons-react';
import { Product } from '@/types/database';

interface ProductFormProps {
  product?: Product | null;
  onSave: () => void;
  onCancel: () => void;
}

export default function ProductForm({ product, onSave, onCancel }: ProductFormProps) {
  const [loading, setLoading] = useState(false);

  const form = useForm({
    initialValues: {
      name: product?.name || '',
      description: product?.description || '',
      price: product?.price || 0,
      images: product?.images || [],
      newImageUrl: '',
      specs: product?.specs ? JSON.stringify(product.specs, null, 2) : '{}',
    },
    validate: {
      name: (value) => (!value ? 'Product name is required' : null),
      price: (value) => (value <= 0 ? 'Price must be greater than 0' : null),
      specs: (value) => {
        try {
          JSON.parse(value);
          return null;
        } catch {
          return 'Invalid JSON format';
        }
      },
    },
  });

  const addImage = () => {
    const newUrl = form.values.newImageUrl.trim();
    if (newUrl && !form.values.images.includes(newUrl)) {
      form.setFieldValue('images', [...form.values.images, newUrl]);
      form.setFieldValue('newImageUrl', '');
    }
  };

  const removeImage = (index: number) => {
    const newImages = form.values.images.filter((_, i) => i !== index);
    form.setFieldValue('images', newImages);
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleSubmit = async (values: typeof form.values) => {
    setLoading(true);

    try {
      let specs = null;
      if (values.specs.trim()) {
        try {
          specs = JSON.parse(values.specs);
        } catch (error) {
          form.setFieldError('specs', 'Invalid JSON format');
          setLoading(false);
          return;
        }
      }

      const productData = {
        name: values.name,
        description: values.description || null,
        price: values.price,
        images: values.images,
        specs,
      };

      const url = product ? `/api/products/${product.id}` : '/api/products';
      const method = product ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      const data = await response.json();

      if (response.ok) {
        notifications.show({
          title: 'Success',
          message: `Product ${product ? 'updated' : 'created'} successfully`,
          color: 'green',
        });
        onSave();
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || `Failed to ${product ? 'update' : 'create'} product`,
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error saving product:', error);
      notifications.show({
        title: 'Error',
        message: 'An unexpected error occurred',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={form.onSubmit(handleSubmit)}>
      <Stack>
        <TextInput
          label="Product Name"
          placeholder="Enter product name"
          required
          {...form.getInputProps('name')}
        />

        <Textarea
          label="Description"
          placeholder="Enter product description"
          rows={3}
          {...form.getInputProps('description')}
        />

        <NumberInput
          label="Price"
          placeholder="0.00"
          min={0}
          step={0.01}
          decimalScale={2}
          fixedDecimalScale
          required
          {...form.getInputProps('price')}
        />

        {/* Image Management Section */}
        <Stack gap="sm">
          <Text fw={500} size="sm">Product Images</Text>

          {/* Add new image */}
          <Group>
            <TextInput
              placeholder="https://example.com/image.jpg"
              value={form.values.newImageUrl}
              onChange={(e) => form.setFieldValue('newImageUrl', e.currentTarget.value)}
              onKeyPress={(e) => e.key === 'Enter' && addImage()}
              style={{ flex: 1 }}
            />
            <Button
              size="sm"
              leftSection={<IconPlus size="1rem" />}
              onClick={addImage}
              disabled={!form.values.newImageUrl.trim()}
            >
              Add Image
            </Button>
          </Group>

          {/* Display existing images */}
          {form.values.images.length > 0 ? (
            <Stack gap="xs">
              {form.values.images.map((imageUrl, index) => (
                <Card key={index} withBorder p="sm">
                  <Group justify="space-between">
                    <Group>
                      {isValidUrl(imageUrl) ? (
                        <Image
                          src={imageUrl}
                          alt={`Product image ${index + 1}`}
                          width={50}
                          height={50}
                          fit="cover"
                          radius="sm"
                          fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yNSAzNUMzMC41MjI4IDM1IDM1IDMwLjUyMjggMzUgMjVDMzUgMTkuNDc3MiAzMC41MjI4IDE1IDI1IDE1QzE5LjQ3NzIgMTUgMTUgMTkuNDc3MiAxNSAyNUMxNSAzMC41MjI4IDE5LjQ3NzIgMzUgMjUgMzVaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K"
                        />
                      ) : (
                        <div style={{
                          width: 50,
                          height: 50,
                          backgroundColor: '#f5f5f5',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: 4
                        }}>
                          <IconPhoto size="1.5rem" color="#9ca3af" />
                        </div>
                      )}
                      <div>
                        <Text size="sm" truncate style={{ maxWidth: 300 }}>
                          {imageUrl}
                        </Text>
                        <Text size="xs" c="dimmed">
                          Image {index + 1}
                        </Text>
                      </div>
                    </Group>
                    <ActionIcon
                      color="red"
                      variant="subtle"
                      onClick={() => removeImage(index)}
                    >
                      <IconTrash size="1rem" />
                    </ActionIcon>
                  </Group>
                </Card>
              ))}
            </Stack>
          ) : (
            <Alert color="blue" variant="light">
              <Text size="sm">No images added yet. Add image URLs above to display product images.</Text>
            </Alert>
          )}
        </Stack>

        <JsonInput
          label="Specifications (JSON)"
          placeholder='{"color": "blue", "size": "medium"}'
          validationError="Invalid JSON"
          formatOnBlur
          autosize
          minRows={4}
          {...form.getInputProps('specs')}
        />

        <Group justify="flex-end" mt="md">
          <Button variant="subtle" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" loading={loading}>
            {product ? 'Update' : 'Create'} Product
          </Button>
        </Group>
      </Stack>
    </form>
  );
}

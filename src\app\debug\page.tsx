'use client';

import { useState, useEffect } from 'react';
import { Container, Title, Text, Stack, Button, Code, Alert } from '@mantine/core';

export default function DebugPage() {
  const [mounted, setMounted] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  useEffect(() => {
    setMounted(true);
    
    // Check if we can access basic APIs
    const info = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      location: window.location.href,
      localStorage: typeof localStorage !== 'undefined',
      fetch: typeof fetch !== 'undefined',
      console: typeof console !== 'undefined',
    };
    
    setDebugInfo(info);
  }, []);

  const testAPI = async () => {
    try {
      const response = await fetch('/api/debug');
      const data = await response.json();
      console.log('API Response:', data);
      alert('API test successful - check console for details');
    } catch (error) {
      console.error('API test failed:', error);
      alert('API test failed - check console for details');
    }
  };

  if (!mounted) {
    return <div>Loading debug page...</div>;
  }

  return (
    <Container size="md" my={40}>
      <Stack>
        <Title>Debug Information</Title>
        
        <Alert color="blue">
          <Text fw={500}>Page Status</Text>
          <Text size="sm">This page is rendering correctly if you can see this message.</Text>
        </Alert>

        <Button onClick={testAPI}>Test API Connection</Button>

        <div>
          <Text fw={500} mb="xs">Environment Info:</Text>
          <Code block>
            {JSON.stringify(debugInfo, null, 2)}
          </Code>
        </div>

        <div>
          <Text fw={500} mb="xs">Quick Links:</Text>
          <Stack gap="xs">
            <Button component="a" href="/" variant="subtle">Home Page</Button>
            <Button component="a" href="/login" variant="subtle">Login Page</Button>
            <Button component="a" href="/setup" variant="subtle">Setup Page</Button>
            <Button component="a" href="/api/debug" variant="subtle" target="_blank">Debug API</Button>
          </Stack>
        </div>
      </Stack>
    </Container>
  );
}

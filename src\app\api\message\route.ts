import { NextRequest, NextResponse } from 'next/server';
import { processIncomingMessage } from '@/lib/messageProcessor';

/**
 * POST /api/message - Handle incoming messages from third-party platforms
 * 
 * Expected request body:
 * {
 *   "sender_id": "string",
 *   "message": "string"
 * }
 * 
 * Response:
 * {
 *   "reply": "string"
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { sender_id, message } = body;
    
    if (!sender_id) {
      return NextResponse.json(
        { error: 'sender_id is required' },
        { status: 400 }
      );
    }
    
    if (!message) {
      return NextResponse.json(
        { error: 'message is required' },
        { status: 400 }
      );
    }
    
    if (typeof sender_id !== 'string' || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'sender_id and message must be strings' },
        { status: 400 }
      );
    }
    
    // Log incoming message for debugging
    console.log('Third-party message received:', {
      sender_id,
      message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
      timestamp: new Date().toISOString()
    });
    
    // Process the message and generate response
    const responseText = await processIncomingMessage(
      sender_id,
      message,
      'third_party',
      {
        source: 'api_endpoint',
        user_agent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      }
    );
    
    // Return response in the expected format for third-party platforms
    return NextResponse.json({
      reply: responseText
    });
    
  } catch (error) {
    console.error('Message processing error:', error);
    
    // Return a generic error response that third-party platforms can handle
    return NextResponse.json(
      { 
        error: 'Failed to process message',
        reply: 'Sorry, I encountered an error processing your message. Please try again later.'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/message - Return API documentation
 */
export async function GET() {
  return NextResponse.json({
    name: 'Message Processing API',
    description: 'Endpoint for processing incoming messages from third-party platforms like Make.com, n8n, etc.',
    version: '1.0.0',
    endpoints: {
      'POST /api/message': {
        description: 'Process an incoming message and return an automated response',
        request_body: {
          sender_id: 'string (required) - Unique identifier for the message sender',
          message: 'string (required) - The text content of the message'
        },
        response: {
          reply: 'string - The generated response text'
        },
        example_request: {
          sender_id: '1234567890',
          message: 'How much is the price?'
        },
        example_response: {
          reply: 'Thank you for your inquiry! Please let me know which specific product you\'re interested in and I\'ll provide you with pricing information.'
        }
      }
    },
    integration_notes: {
      'Make.com': 'Use the HTTP module to send POST requests to this endpoint',
      'n8n': 'Use the HTTP Request node with POST method',
      'Zapier': 'Use Webhooks by Zapier with POST method',
      'General': 'Any platform that can send HTTP POST requests with JSON payload'
    }
  });
}

# Testing Guide

This guide will help you test all features before deploying to Vercel.

## Prerequisites

1. **Environment Setup**: Copy `.env.local.example` to `.env.local` and fill in your values
2. **Database Setup**: Ensure Supabase is configured and tables are created
3. **LLM Provider**: Configure at least one LLM provider (Gemini recommended for free tier)

## Step-by-Step Testing

### 1. Initial Setup and Database

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Visit the setup page**: http://localhost:3000/setup
   - Check all system components are green
   - If admin user doesn't exist, click "Create Admin User"
   - Verify database connection and tables

3. **Test the homepage**: http://localhost:3000
   - Should show system status
   - All status indicators should be green or yellow (not red)

### 2. Authentication Testing

1. **Visit login page**: http://localhost:3000/login
   - Should not be blank (if blank, check console for errors)
   - Default credentials: `admin` / `admin123`

2. **Test login process**:
   - Enter credentials and click login
   - Should redirect to admin dashboard
   - Check that navigation works

3. **Test logout**:
   - Click logout in admin panel
   - Should redirect to homepage

### 3. Admin Dashboard Testing

1. **Dashboard Overview**: http://localhost:3000/admin
   - Should show system health status
   - Check for any alerts or warnings
   - Verify all health indicators are working

2. **Product Management**: http://localhost:3000/admin/products
   - Create a new product with multiple images
   - Edit an existing product
   - Verify image management works (add/remove images)

3. **Settings Page**: http://localhost:3000/admin/settings
   - Test password change functionality
   - Test creating a new admin user
   - Verify user list displays correctly

### 4. Chat Interface Testing

1. **Visit chat page**: http://localhost:3000/admin/chat
   - Interface should load without errors

2. **Test basic chat**:
   - Send a simple message: "Hello"
   - Should receive a response (fallback if no LLM configured)

3. **Test product inquiries**:
   - Ask: "What products do you have?"
   - Ask: "How much does [product name] cost?"
   - Verify responses are relevant

4. **Test correction system**:
   - Send a message that gets a poor response
   - Click the edit icon on the bot response
   - Enter a better response and save
   - Send the same message again - should use the corrected response

### 5. Message Processing Testing

1. **Test API endpoint directly**:
   ```bash
   curl -X POST http://localhost:3000/api/message \
     -H "Content-Type: application/json" \
     -d '{"sender_id": "test123", "message": "Hello, what products do you sell?"}'
   ```

2. **Test via test endpoint**: http://localhost:3000/api/test-message
   - Use the interface to send test messages
   - Verify responses are saved to database

3. **Check message history**: http://localhost:3000/admin/messages
   - Verify all test messages appear
   - Check response quality and confidence scores

### 6. LLM Integration Testing (if configured)

1. **Verify LLM configuration**:
   - Check setup page shows LLM as "CONFIGURED"
   - Test with a complex product question

2. **Test LLM responses**:
   - Ask detailed questions about products
   - Verify responses are contextual and helpful
   - Check confidence scores are reasonable

3. **Test memory learning**:
   - Ask a question, get LLM response
   - If confidence is high, the response should be saved to memory
   - Ask the same question again - should use memory instead of LLM

## Common Issues and Solutions

### Login Page is Blank
- Check browser console for JavaScript errors
- Verify all environment variables are set
- Check if admin user exists in database
- Visit `/setup` page to diagnose issues

### 401 Unauthorized Errors
- This is normal when not logged in
- Check JWT_SECRET is set in environment
- Verify Supabase configuration

### LLM Not Working
- Check API key is valid and has credits
- Verify LLM_PROVIDER matches your configured provider
- Check network connectivity to LLM provider

### Database Connection Issues
- Verify Supabase URL and keys are correct
- Check if tables exist (run schema.sql)
- Ensure service role key has proper permissions

### Chat Interface Not Responding
- Check browser console for errors
- Verify `/api/message` endpoint is working
- Test with simple messages first

## Pre-Deployment Checklist

- [ ] All tests pass locally
- [ ] Admin login works
- [ ] Chat interface responds correctly
- [ ] Product management works
- [ ] Settings page functions properly
- [ ] No console errors in browser
- [ ] Environment variables are documented
- [ ] Database schema is up to date
- [ ] LLM provider is configured (optional but recommended)

## Deployment to Vercel

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Deploy to Vercel**:
   - Connect your GitHub repository
   - Add all environment variables from `.env.local`
   - Deploy

3. **Post-deployment testing**:
   - Visit your deployed URL
   - Test login functionality
   - Verify chat interface works
   - Check all admin features

4. **Create admin user on production**:
   - Visit `https://your-app.vercel.app/setup`
   - Create admin user if needed
   - Change default password immediately

## Security Notes

- Change default admin password immediately after deployment
- Use strong passwords for all admin accounts
- Keep API keys secure and never commit them to git
- Regularly monitor system health and logs
- Consider setting up monitoring alerts for production

## Support

If you encounter issues:
1. Check the browser console for errors
2. Review server logs in Vercel dashboard
3. Verify all environment variables are set correctly
4. Test individual components in isolation
5. Check database connectivity and permissions

// Configuration system for LLM providers and application settings

export interface LLMConfig {
  provider: 'gemini' | 'openrouter' | 'openai' | 'anthropic';
  apiKey: string;
  model?: string;
  baseUrl?: string;
}

export interface AppConfig {
  llm: LLMConfig;
  replyTone: 'professional' | 'friendly' | 'casual' | 'formal';
  confidenceThreshold: number; // 0.0 to 1.0
  maxResponseLength: number;
  enableAutoReply: boolean;
  fallbackMessage: string;
}

// Default configuration
const defaultConfig: AppConfig = {
  llm: {
    provider: 'gemini',
    apiKey: process.env.GEMINI_API_KEY || '',
    model: 'gemini-pro',
  },
  replyTone: 'friendly',
  confidenceThreshold: 0.8,
  maxResponseLength: 500,
  enableAutoReply: true,
  fallbackMessage: 'Thank you for your message. Our team will get back to you soon!',
};

// Load configuration from environment variables and defaults
export function loadConfig(): AppConfig {
  const provider = (process.env.LLM_PROVIDER as LLMConfig['provider']) || defaultConfig.llm.provider;
  
  let apiKey = '';
  let model = '';
  let baseUrl = '';

  switch (provider) {
    case 'gemini':
      apiKey = process.env.GEMINI_API_KEY || '';
      model = process.env.GEMINI_MODEL || 'gemini-pro';
      break;
    case 'openrouter':
      apiKey = process.env.OPENROUTER_API_KEY || '';
      model = process.env.OPENROUTER_MODEL || 'google/gemini-pro';
      baseUrl = 'https://openrouter.ai/api/v1';
      break;
    case 'openai':
      apiKey = process.env.OPENAI_API_KEY || '';
      model = process.env.OPENAI_MODEL || 'gpt-3.5-turbo';
      baseUrl = 'https://api.openai.com/v1';
      break;
    case 'anthropic':
      apiKey = process.env.ANTHROPIC_API_KEY || '';
      model = process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229';
      break;
  }

  return {
    llm: {
      provider,
      apiKey,
      model,
      baseUrl,
    },
    replyTone: (process.env.REPLY_TONE as AppConfig['replyTone']) || defaultConfig.replyTone,
    confidenceThreshold: parseFloat(process.env.CONFIDENCE_THRESHOLD || '0.8'),
    maxResponseLength: parseInt(process.env.MAX_RESPONSE_LENGTH || '500'),
    enableAutoReply: process.env.ENABLE_AUTO_REPLY !== 'false',
    fallbackMessage: process.env.FALLBACK_MESSAGE || defaultConfig.fallbackMessage,
  };
}

// Get tone-specific system prompts
export function getSystemPrompt(tone: AppConfig['replyTone']): string {
  const basePrompt = `You are a helpful customer service assistant for an e-commerce business. 
You have access to product information and should help customers with their inquiries.
Always be accurate and helpful. If you don't know something, say so politely.`;

  const tonePrompts = {
    professional: `${basePrompt} Maintain a professional and business-like tone in all responses.`,
    friendly: `${basePrompt} Use a warm, friendly, and approachable tone. Be conversational but helpful.`,
    casual: `${basePrompt} Keep responses casual and relaxed, like talking to a friend. Use simple language.`,
    formal: `${basePrompt} Use formal language and proper business etiquette in all communications.`,
  };

  return tonePrompts[tone];
}

// Validate configuration
export function validateConfig(config: AppConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.llm.apiKey) {
    errors.push(`API key is required for ${config.llm.provider}`);
  }

  if (config.confidenceThreshold < 0 || config.confidenceThreshold > 1) {
    errors.push('Confidence threshold must be between 0 and 1');
  }

  if (config.maxResponseLength < 50 || config.maxResponseLength > 2000) {
    errors.push('Max response length must be between 50 and 2000 characters');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Export the loaded configuration (lazy loaded)
let _config: AppConfig | null = null;
export const config = new Proxy({} as AppConfig, {
  get(_target, prop) {
    if (!_config) {
      _config = loadConfig();
    }
    return _config[prop as keyof AppConfig];
  }
});
